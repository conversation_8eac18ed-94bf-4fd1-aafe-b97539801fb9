/* styles.css */
body {
  margin: 0;
  font-family: 'Sora', sans-serif;
  background-color: #f6f0cd;
  color: #54524c;
}

.hero {
  position: relative;
  text-align: center;
  color: #f6f0cd;
}

.hero-img {
  width: 100%;
  height: auto;
  object-fit: cover;
  filter: brightness(0.6);
}

.hero-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(84, 82, 76, 0.7);
  padding: 1rem 2rem;
  border-radius: 1rem;
}

.navbar {
  background-color: #7a786f;
  padding: 1rem;
  text-align: center;
}

.navbar ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.navbar a {
  text-decoration: none;
  color: #f6f0cd;
  font-weight: bold;
}

.section {
  padding: 2rem;
  background-color: #d6d2c7;
  margin-bottom: 1rem;
  border-radius: 1rem;
  max-width: 900px;
  margin: 2rem auto;
}

.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.card {
  background-color: #f6f0cd;
  border: 1px solid #7a786f;
  border-radius: 1rem;
  padding: 1rem;
  text-align: center;
}

.card img {
  width: 100%;
  border-radius: 0.5rem;
}

footer {
  text-align: center;
  padding: 1rem;
  background-color: #7a786f;
  color: #f6f0cd;
}
